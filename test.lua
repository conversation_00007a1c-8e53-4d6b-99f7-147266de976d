--local module=require "module.module"
--local ui = require "ui.ui"
--local cfg = require "cfg._cfgs"
--local uimgr = require "ui.uimgr"
--local common = require "common.common"
--
--local test={}
--
------ 重新reload panelui
--function test.reloadui(panelname)
--    local assetpanel = cfg.ui.assetpanel.get(panelname)
--    if assetpanel then
--        if ui[panelname].Uninit then
--            ui[panelname]:Uninit()
--        end
--        ui[panelname]:Hide()
--        uimgr.panelpool:clear()
--        uimgr.worldpanelpool:clear()
--
--        package.loaded[assetpanel.luapath] = nil
--        ui[panelname] = uimgr.CreateSingltonPanel(assetpanel)
--
--        if ui[panelname].Init then
--            ui[panelname]:Init()
--        end
--        print(panelname.." reload done")
--    else
--        common.logger.Error("invaliad panelname")
--    end
--end
--
------ 重新reload uicom 组件。注意：父级对该组件引用如果还是旧值，则也需reload父级。建议：对子级的require写在父级的AfterAwake中。
--function test.reloaduicom(componentpath)
--    if package.loaded[componentpath] then
--        package.loaded[componentpath] = nil
--        local comCls = require(componentpath)
--        print(componentpath .." reload done")
--    else
--        common.logger.Error("invaliad componentname: " .. componentpath)
--    end
--end
--
------ 重新reload module 并拷贝数据。注意module需是单例。
--function test.reloadmodule(modulepath, modulename)
--    if package.loaded[modulepath] then
--        local oldModuleCls = package.loaded[modulepath]
--        package.loaded[modulepath] = nil
--        local newModuleCls = require(modulepath)
--        test.modulecopydata(oldModuleCls, newModuleCls)
--        if modulename then
--            module[modulename] = newModuleCls
--        end
--        print(modulepath .." reload done")
--    else
--        common.logger.Error("invaliad modulepath: " .. modulepath)
--    end
--end
--
--function test.modulecopydata(oldmodule, newmodule)
--    if not oldmodule or not newmodule then return end
--    for key, value in pairs(oldmodule) do
--        if type(value) ~= "function" then
--            newmodule[key] = value
--        end
--    end
--end
--
--local i = 0
--function test.main()
--    --[[
--    reload ui 示例
--        test.reloadui('panelskill')
--    reload com 示例
--        test.reloaduicom('ui.skill.SkillPageItem')
--    reload module 示例
--        test.reloadmodule('ui.uiutils')
--        test.reloadmodule('module.skill.Skill', 'skill')
--    ]]--
--    local UILayer = require("ui.UILayer")
--    common.logger.Print(UILayer.stack)
--    --test.reloadmodule('module.ModuleEvent','event')
--    --test.reloadmodule('ui.uiutils')
--    --test.reloadmodule('module.growth.skill.SkillMsg','skillmsg')
--    --test.reloadmodule('module.growth.skill.Skill','skill')
--    --test.reloadmodule('module.actor.ctrl.selectctr.SelectedTarget','selectedtarget')
--    --test.reloadmodule('module.actor.controller.UserInput','userinput')
--    --test.reloadmodule('module.actor.ctrl.InputController','inputcontrol')
--    --test.reloadmodule('module.actor.sound.SoundMgr','soundmgr')
--    --test.reloadmodule('module.actor.msg.EffectMsgHandler',nil)
--    --test.reloadmodule("module.actor.ActorModule",nil)
--
--    --test.reloadmodule('module.actor.ctrl.InputController', 'inputcontrol')
--    --test.reloaduicom('ui.skill.SkillPageItem')
--    --test.reloaduicom('ui.skill.SkillLearn')
--    --test.reloaduicom('ui.skill.SkillInfo')
--    --test.reloaduicom('ui.skill.SkillPage1')
--    --test.reloaduicom('ui.skill.SkillLanSetItem')
--    --test.reloaduicom('ui.skill.SkillLanItem')
--    --test.reloaduicom('ui.skill.SkillLan')
--    --test.reloaduicom('ui.operation.SkillItem')
--    --test.reloaduicom('ui.operation.SkillView')
--    --test.reloaduicom('ui.operation.BuffSet')
--
--    --test.reloadui('paneltasktips')
--    --test.reloadui('panelskill')
--    --test.reloadui('paneloperation1')
--    --ui.paneloperation1:Show()
--    --ui.panelskill:Show()
--
--    --[[ 测试增加BOSS死亡慢镜头效果
--    local passtime = 0
--    local flag = true
--    local removeFun
--    removeFun =module.event.evt_update:Register(function(delta)
--        passtime = passtime + delta
--        if passtime > 2 and flag then
--            flag = false
--            UnityEngine.Time.timeScale = 0.1
--            local skillCfg = cfg.skill.skill.get(2001)
--            module.inputcontrol.CastSkill(skillCfg)
--        elseif passtime > 6 then
--            UnityEngine.Time.timeScale = 1
--            removeFun()
--            removeFun = nil
--        end
--    end)
--    --]]--
--
--    --[[ 测试SSyncBuffAdd
--    local passtime = 0
--    local count = 0
--    local uuid = 99999
--    module.event.evt_update:Register(function(delta)
--        passtime = passtime + delta
--        if passtime > 0.5 and count < 8 then
--
--            local effectmsg = require "module.actor.msg.EffectMsgHandler"
--            local msg = {
--                fighterid = 1310721,
--                buffid = uuid,
--                buffcfgid = 2000001,
--            }
--            effectmsg.onmsg_SSyncBuffAdd(msg)
--            passtime = passtime - 1
--            count = count + 1
--            uuid = uuid + 100
--        end
--    end)
--    --]]--
--
--    --[[ 测试SSyncEffectCreateObj
--    local effectmsg = require "module.actor.msg.EffectMsgHandler"
--    local msg = {
--        fighterid = module.player.controlledfighter.uuid,
--        objtype = 2,
--        cfgid = 178,
--        uuid = 999999999,
--        pos = module.player.controlledfighter.transform.position,
--        dir = module.player.controlledfighter.transform.forward
--    }
--    effectmsg.onmsg_SSyncEffectCreateObj(msg)
--    --]]--
--
--    --[[ 测试击退，击飞，死亡，击退死亡
--    local startPos = module.player.controlledfighter.transform.position
--    local endPos = startPos + UnityEngine.Vector3(0, 0, 5)
--    msg={
--        casterid = module.player.controlledfighter.uuid,
--        targetid = -103,
--        hp = 10,
--        damage = 100,
--        hpremove = 1000,
--        iscrit = 0,
--        ismiss = 0,
--        hurtstate = 1,
--        curve = {
--            startpos = {x = startPos.x, y = startPos.y, z = startPos.z},
--            stoppos = {x = endPos.x, y = endPos.y, z = endPos.z},
--            endpos = {x = endPos.x, y = endPos.y, z = endPos.z},
--            height = 0,
--            stoptime = 0.15,
--            endtime = 0.15,
--        },
--        behittype = 1,
--    }
--    local effectMsgHandler = require "module.actor.msg.EffectMsgHandler"
--    effectMsgHandler.onmsg_SSyncEffectDamage(msg)
--    ]]--
--
--    --[[ 测试
--    local startPos = module.player.controlledfighter.transform.position
--    local endPos = startPos + UnityEngine.Vector3(0, 0, 5)
--    msg={
--        casterid = module.player.controlledfighter.uuid,
--        targetid = -307,
--        hp = 0,
--        damage = 100,
--        hpremove = 1000,
--        iscrit = 0,
--        ismiss = 0,
--        hurtstate = 0,
--        curve = {
--            startpos = {x = startPos.x, y = startPos.y, z = startPos.z},
--            stoppos = {x = endPos.x, y = endPos.y, z = endPos.z},
--            endpos = {x = endPos.x, y = endPos.y, z = endPos.z},
--            height = 0,
--            stoptime = 0.15,
--            endtime = 0.15,
--        },
--        behittype = 0,
--    }
--    local effectMsgHandler = require "module.actor.msg.EffectMsgHandler"
--    effectMsgHandler.onmsg_SSyncEffectDamage(msg)
--    ]]--
--
--    --[[ 测试 冲锋效果
--    local pos = module.player.controlledfighter.transform.position + UnityEngine.Vector3(0,0,10)
--    local msg={
--        casterfighterid = module.player.controlledfighter.uuid,
--        pos = {x = pos.x, y = pos.y, z = pos.z},
--        dir = module.player.controlledfighter.transform.forward,
--        duration = 0.2,
--        rotatable = 0,
--        targetpos = {x = pos.x, y = pos.y, z = pos.z},
--        speed = 2,
--    }
--    local effectMsgHandler = require "module.actor.msg.EffectMsgHandler"
--    effectMsgHandler.onmsg_SSyncEffectLocomotion(msg)
--    ]]--
--
--end
--
--test.main()
--return test
--
--local aa = "Environment_123"
--local s, e = string.find(aa, "Environment_")
--print(s, e)

--debug.sethook()
local ui = require "ui.ui"
ui.panelhomework:OpenPanel("mingshi")

--
--function a.testF6()
--    common.statistics.Start();
--
--end
--
--function a.testF7()
--    common.statistics.End();
--end