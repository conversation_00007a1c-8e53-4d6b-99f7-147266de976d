local res = require "res.res"


local function noload()
    if res.__old_load_ab then
        return
    end

    res.__old_load_ab = res.__load_ab
    res.__old_load_asset_at_ab = res.__load_asset_at_ab

    res.__load_ab = function (assetinfo, callback)
        callback(nil, "test noload")
    end

    res.__load_asset_at_ab = function (assetinfo, ab, aberr, callback)
        callback(nil, "test noload")
    end
end

local function recover()
    if  res.__old_load_ab then
        res.__load_ab = res.__old_load_ab
        res.__load_asset_at_ab = res.__old_load_asset_at_ab

        res.__old_load_ab = nil
        res.__old_load_asset_at_ab = nil
    end
end

noload()


