local GameNpc = {}
local module = require "module.module"

-- 状态枚举
GameNpc.State = {
    Sleep = 1,
    Waiting = 2,
    BeHit = 3,
    Qte = 4,
    End = 5,
}

-- 配置表
GameNpc.Config = {
    -- 思考时间配置
    thinkTime = {
        min = 1,
        max = 2
    },
    -- QTE反应时间配置
    qteActionTime = {
        min = 0.5,
        max = 1.5
    },
    -- NPC作弊率（读取玩家指令的概率）
    cheatRate = 0.99,
    -- QTE失败率
    qteFailRate = 0.1,
    -- QTE间隔时间
    qteInterval = 0.5
}

function GameNpc:Init()
    -- 初始化实例变量
    self.nowState = GameNpc.State.Sleep
    self.waitingTime = 0
    self.qteCount = 0
    self.nextQteTime = 0
    self.qteThinkTime = 0
    self.nowDirection = 0
    self.qteSuccess = false
    self.isWaitingQte = false

    -- 注册更新事件
    module.event.evt_update:Register(GameNpc.Update, self)
end

function GameNpc:Update(deltatime)
    if not deltatime or deltatime <= 0 then
        return
    end

    if self.nowState == GameNpc.State.Waiting then
        self.waitingTime = self.waitingTime - deltatime
        -- 如果到了思考时间
        if self.waitingTime <= 0 then
            self:UpdateWaiting()
        end

    elseif self.nowState == GameNpc.State.Qte then
        -- 检查QTE是否全部完成
        if self.qteCount <= 0 then
            self:QTEFinishAction()
            return
        end

        -- 更新QTE间隔时间
        if self.nextQteTime > 0 then
            self.nextQteTime = self.nextQteTime - deltatime
            if self.nextQteTime <= 0 then
                self:UpdateQte()
            end
        elseif self.nextQteTime == 0 then
            -- 立即开始第一个QTE
            self:UpdateQte()
        end
        -- nextQteTime == -1 表示正在进行QTE，等待NPC反应

        -- 更新NPC思考时间
        if self.qteThinkTime > 0 and not self.isWaitingQte then
            self.qteThinkTime = self.qteThinkTime - deltatime

            if self.qteThinkTime <= 0 then
                -- 判断这次qte是否成功
                local success = self:QTESuccess()
                self.isWaitingQte = true

                if success then
                    -- 成功,继续下个循环
                    self.qteSuccess = true
                    self.qteCount = self.qteCount - 1
                    print("QTE Success! Remaining: " .. self.qteCount)
                    self:QTESuccessAction()

                    -- 准备下一个QTE
                    if self.qteCount > 0 then
                        self.nextQteTime = GameNpc.Config.qteInterval
                        self.isWaitingQte = false
                    end
                else
                    -- 失败
                    print("QTE Failed")
                    self.qteSuccess = false
                    self:QTEFailAction()
                    return
                end
            end
        end
    end
end

---- 入口 ------
function GameNpc:StartPlay()
    self.nowState = GameNpc.State.Waiting
    self.waitingTime = self:GetNextThinkTime()
    self.qteCount = 0
    self.nextQteTime = 0
    self.qteThinkTime = 0
    self.nowDirection = 0
    self.qteSuccess = false
    self.isWaitingQte = false
    print("NPC开始游戏，进入等待状态")
end

function GameNpc:UpdateWaiting()
    -- 如果到思考时间了，选择防御方向
    self.nowDirection = self:RandomDirection()
    self.waitingTime = self:GetNextThinkTime()
    self:ActionDefence()
end

-- 进行防御
function GameNpc:ActionDefence()
    print("NPC防御方向: " .. self.nowDirection)
end

function GameNpc:UpdateQte()
    if self.qteCount > 0 then
        -- 执行QTE表现
        local qteDirection = self:QTEOnce()
        print("开始QTE，方向: " .. qteDirection)

        -- 重置状态，准备NPC反应
        self.isWaitingQte = false
        self.qteSuccess = true  -- 重置为成功状态

        -- 计算NPC点击QTE的反应时间
        self.qteThinkTime = self:GetQteActionTime()
        print("NPC需要思考时间: " .. self.qteThinkTime .. " 秒")

        -- 设置下次QTE的间隔时间（在当前QTE完成后使用）
        self.nextQteTime = -1  -- 标记为正在进行QTE
    end
end

function GameNpc:GetQteActionTime()
    local config = GameNpc.Config.qteActionTime
    local time = math.random(config.min * 1000, config.max * 1000) / 1000
    return time
end

function GameNpc:GetNextThinkTime()
    local config = GameNpc.Config.thinkTime
    local time = math.random(config.min, config.max)
    return time
end

function GameNpc:RandomDirection()
    local directions = {1, 2, 3, 4}  -- 1=上, 2=下, 3=左, 4=右
    local index = math.random(1, #directions)
    return directions[index]
end


-------- 被打击   -------------------------
-- 返回 是否进入眩晕抵抗
function GameNpc:BeHit(direction, force)
    if not direction or not force then
        print("BeHit参数错误")
        return false
    end

    self.nowState = GameNpc.State.BeHit

    -- 看看这次是不是读指令（作弊）
    local cheatRate = GameNpc.Config.cheatRate
    local cheat = math.random()

    if cheat < cheatRate then
        -- 读指令（作弊成功）
        self.nowDirection = direction
        print("NPC作弊成功，读取了玩家指令: " .. direction)
    else
        -- 随机选择方向
        self.nowDirection = self:RandomDirection()
        print("NPC随机选择防御方向: " .. self.nowDirection)
    end


    -- 判断防御成功or失败
    if self.nowDirection == direction and self:IsHeadDefence() then
        -- 防御成功
        print("NPC阻挡成功！进入QTE环节")
        self:BeginQTE(force)
        return true
    else
        -- 防御失败
        print("NPC防御失败，攻击方向: " .. direction .. ", 防御方向: " .. self.nowDirection)
        self:GameOver()
        return false
    end
end

-- 在缩头状态
function GameNpc:IsHeadDefence()
    return true
end

function GameNpc:GameOver()
    print("NPC被破防，游戏结束")
    self.nowState = GameNpc.State.End
end

function GameNpc:RandomQteDirection()
    local directions = {1, 2, 3, 4}  -- 1=上, 2=下, 3=左, 4=右
    local index = math.random(1, #directions)
    return directions[index]
end

------------------- 进入qte -------------------
function GameNpc:BeginQTE(force)
    self.nowState = GameNpc.State.Qte
    self.qteSuccess = true -- 先默认成功
    self.isWaitingQte = false

    -- 根据force决定QTE数量，这里可以根据实际需求调整
    self.qteCount = 5 --math.max(1, math.min(10, force or 5))
    self.nextQteTime = 0  -- 立即开始第一个QTE
    self.qteThinkTime = 0  -- 重置思考时间

    print("进入眩晕抵抗环节，需要完成 " .. self.qteCount .. " 个QTE")
end

-- 执行一次qte表现
function GameNpc:QTEOnce()
    local qteDirection = self:RandomQteDirection()
    print("显示QTE方向: " .. qteDirection)
    return qteDirection
end

----------- 通知成功还是失败 -------------------
-- 判断这次qte是否成功
function GameNpc:QTESuccess()
    local failRate = GameNpc.Config.qteFailRate
    local randomValue = math.random()

    if randomValue > failRate then
        -- 成功
        print("NPC QTE点击成功")
        return true
    else
        print("NPC QTE点击失败")
        return false
    end
end

-- qte失败的表现
function GameNpc:QTEFailAction()
    self.nowState = GameNpc.State.End
    print("NPC QTE失败，眩晕抵抗失败")
end

-- qte成功的表现
function GameNpc:QTESuccessAction()
    print("NPC单次QTE成功")
end

--- qte全部完成 -----------------------
function GameNpc:QTEFinishAction()
    self.nowState = GameNpc.State.End
    print("NPC成功完成所有QTE，眩晕抵抗成功！")
end

return GameNpc