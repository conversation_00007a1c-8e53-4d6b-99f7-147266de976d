local module = require "module.module"
local UIComponent = require "ui.UIComponent"
local ExampleComponent = UIComponent:New()
function ExampleComponent:Awake()
end
function ExampleComponent:AfterAwake()
end
function ExampleComponent:Enable()
    self:RegisterEvent(module.event.evt0, function() self:Show() end)
    self:RegisterEvent(module.event.evt1, function() self:Render1() end)
    self:RegisterEvent(module.event.evt2, function() self:Render2() end)
end
function ExampleComponent:SetData(data)
    self:SetData1(data.data1)
    self:SetData2(data.data2)
end
function ExampleComponent:Render()
    self:Render1()
    self:Render2()
end
function ExampleComponent:SetData1(data1)
    self.data1 = data1
end
function ExampleComponent:Render1()
    --render self.data1
end
function ExampleComponent:SetData(data2)
    self.data2 = data2
end
function ExampleComponent:Render2()
    --render self.data2
end
return ExampleComponent

