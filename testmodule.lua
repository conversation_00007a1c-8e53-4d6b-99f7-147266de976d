local testmodule = {}
local module = require "module.module"
local print = require 'common.Logger'.Print
local Vector2 = UnityEngine.Vector2

function testmodule:Init()
    --module.event.begin_jump:Register(testmodule.OnBeginJump)
    --module.event.begin_jump:Register(testmodule.OnBeginJump)

    --module.event.player_jump_end:Register(testmodule.OnJumpEnd)
    testmodule.inair = false
    testmodule.beginpos = Vector2.zero
    testmodule.endpos = Vector2.zero
    testmodule.open = false
    testmodule.lastdis = 0
end

function testmodule.OnBeginJump(location)
    if testmodule.inair then
        return
    end
    testmodule.inair = true
    testmodule.beginpos = location
end

function testmodule.OnJumpEnd(location)
    if testmodule.inair == false then
        return
    end
    testmodule.inair = false
    testmodule.endpos = location
    testmodule.lastdis = Vector2.Distance(testmodule.beginpos, testmodule.endpos)
    if (testmodule.lastdis > 100) then
        testmodule.open = true
        module.notice.NoteCenterTopPlainText("Great! You've Jumped For " .. testmodule.lastdis  .. " Meters")
    end
end

return testmodule
