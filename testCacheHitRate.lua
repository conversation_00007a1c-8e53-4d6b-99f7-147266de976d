local common = require "common.common"
local res = require "res.res"
local Pool = require "res.Pool"

local console = UnityEngine.GameObject.Find("/Boot/ADebugServer"):GetComponent(ADebugger.ADebugConsole)
console.enabled = true
console:ClearProperties()

local function fps()
    return common.fps.current
end

console:AddPropertyChart("fps", fps, 0, 30, 30, 300, 1, 30)

local function addProp(cache, txt)
    local function access()
        return cache.all_access
    end

    local function hit_rate()
        return math.floor(cache.all_hit * 100 / cache.all_access)
    end

    local last_hit = cache.all_hit
    local last_access = cache.all_access
    local function hit_rate_per()
        local access = cache.all_access - last_access
        local rate = 100
        if access > 0 then
            rate = math.floor((cache.all_hit - last_hit) * 100 /  access)
        end
        last_hit = cache.all_hit
        last_access = cache.all_access
        return rate
    end

    console:AddProperty(txt.." access", access)
    console:AddProperty(txt.." hit rate", hit_rate)
    console:AddPropertyChart(txt.." hit rate per 10s", hit_rate_per, 0, 100, 300, 300, 1, 30)
end


addProp(res, "res")
addProp(Pool, "Pool")
