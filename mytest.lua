
local timer = require "common.Timer".globalTimer
local cfg = require "cfg._cfgs"
local ui = require "ui.ui"
local common = require "common.common"
local module = require "module.module"
local pp = common.logger.Print
-- 技能声效




local  a = {}
local gamenpc = require "test.tmp"



function a.testF6()
    module.gamenpc:StartPlay()



    --local snapshot = require "snapshot"
    --local snapshot_utils = require "snapshot_utils"
    --local construct_indentation = snapshot_utils.construct_indentation
    --local print_r = require "print_r"
    --
    --local S1 = snapshot()
    --
    --local tmp = {
    --    player = {
    --        uid = 1,
    --        camps = {
    --            {campid = 1},
    --            {campid = 2},
    --        },
    --    },
    --    player2 = {
    --        roleid = 2,
    --    },
    --    [3] = {
    --        player1 = 1,
    --    },
    --}
    --
    --local a = {}
    --local c = {}
    --a.b = c
    --c.d = a
    --
    --local msg = "bar"
    --local foo = function()
    --    print(msg)
    --end
    --
    --local co = coroutine.create(function ()
    --    print("hello world")
    --end)
    --
    --local S2 = snapshot()
    --
    --local diff = {}
    --for k,v in pairs(S2) do
    --    if not S1[k] then
    --        diff[k] = v
    --    end
    --end
    --
    --print_r(diff)
    --print("===========================")
    --
    --local result = construct_indentation(diff)
    --print_r(result)
end

function a.testF7()
    --common.statistics.End();
    print(module.gamenpc:BeHit(1, 1))
end

return a