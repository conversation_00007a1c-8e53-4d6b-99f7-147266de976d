local print = print
local debug = debug
local getinfo = debug.getinfo
local debugClass = true

local print_traceback = function (...)
    print(...)
    print(debug.traceback())
end

local isPrivateMember = function (memberName)
    if string.sub(memberName, 1, 1) == "_" then return true end
end

local isCallInClass = function (tmpCls, layer, callFuncName)
    local funcinfo = getinfo(layer)
    local name = funcinfo.name or "anonymous"
    local func = funcinfo.func

    callFuncName = callFuncName or name

    local tmpFunc
    while tmpCls do
        tmpFunc = rawget(tmpCls, name)
        if tmpFunc and tmpFunc == func and callFuncName == name then
            return true
        else
            tmpCls = getmetatable(tmpCls)
        end
    end
    return false
end

local isCallInClassCtor = function (tmpCls, layer)
    return isCallInClass(tmpCls, layer + 1, "ctor")
end

local getPrivateObj = function (cls)
    if not debugClass then return cls end

    local privateObj = {}
    setmetatable(privateObj, cls)
    privateObj.__index = function (po, k)
        local v = privateObj[k]
        if v then
            -- 检查是否在类的成员函数中调用私有成员
            if isCallInClass(privateObj, 3) then
                return v
            else
                return print_traceback("error at __index: can not visit private member(" .. k .. ") outside Class " .. cls.__name) 
            end
        end
    end

    privateObj.__newindex = function (po, k, _v)
        local v = privateObj[k]
        if not v then -- new member
            -- 检查是不是在类的ctor中新建成员
            if not isCallInClassCtor(privateObj, 3) then
                print_traceback("error at __newindex: can not new a member(" .. k .. ") outside Class ctor ")
            else
                rawset(isPrivateMember(k) and privateObj or po, k, _v)
            end
        else -- set member
            -- 检查是不是在类的成员函数中set成员
            if not isCallInClass(privateObj, 3) then
                if isPrivateMember(k) then
                    print_traceback("error at __newindex: can not set private member(" .. k .. ") outside Class " .. cls.__name, _v) 
                else
                    rawset(po, k, _v)
                end
            else
                rawset(isPrivateMember(k) and privateObj or po, k, _v)
            end
        end
    end
    return privateObj
end

local class = function (clsName, super)
    local cls = {}
    if super then setmetatable(cls, super) end
    cls.super = super
    cls.__name = clsName
    cls.__index = cls

    function cls:ctor() end
    function cls:_ctor() 
        if super then super._ctor(self) end
        cls.ctor(self)
    end

    function cls:New()
        local publicObj = {}
        setmetatable(publicObj, getPrivateObj(cls))
        publicObj:_ctor()
        return publicObj
    end
    return cls
end

return class


