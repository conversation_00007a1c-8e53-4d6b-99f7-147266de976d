local cfg = require("cfg._cfgs")
local common = require("common.common")
local module = require("module.module")
local loader = require "res.loader"
local ui = require("ui.ui")

local uimgr = require("ui.uimgr")
local UIComponent = require("ui.UIComponent")
local UILayer = require("ui.UILayer")
local uicomponent_private = UIComponent._private
local private = UIComponent._private
common.logger.Print(uimgr.layers.module)
ui.panelrole:Hide()
ui.panelrole:Show()
ui.panelfriend:Show()
ui.panelfriend:Show()
ui.panelfriend:Show()
ui.panelrole:Hide()
ui.panelactivity:Hide()
ui.panelactivity:Show()
ui.panelfriend:Hide()
ui.panelfriend:Hide()
ui.panelfriend:Hide()
ui.panelfriend:Show()
ui.panelfriend:Hide()
common.logger.Print(uimgr.layers.module)

