local cfg = require "cfg._cfgs"
local common = require "common.common"

local checkStunt = {}

function checkStunt.Check()
    --- book.csv
    local bookall = cfg.skill.stunt.book.all
    for id, bookcfg in pairs(bookall) do
        if not cfg.item.commonitem.get(id) then
            common.logger.Error(" @唐鑫，绝世武功，book.csv中id={0} 在commonitem.csv里不存在", id)
        elseif bookcfg.RefType == cfg.skill.stunt.booktype.Book then
            if not bookcfg.NullableRefTid then
                common.logger.Error(" @唐鑫，绝世武功，book.csv中id={0}记录中绝技tid={1} 在skill.csv里不存在", id, bookcfg.tid)
            end
            if not cfg.skill.stunt.stunt.get(bookcfg.tid, 1) then
                common.logger.Error(" @唐鑫，绝世武功，book.csv中id={0}记录中绝技tid={1} 在stunt.csv里不存在", id, bookcfg.tid)
            end
        end
    end
    
    --- group.csv
    local groupall = cfg.skill.stunt.group.all
    for id, groupcfg in pairs(groupall) do
        local lastlevel = 0
        for _, attrinfo in ipairs(groupcfg.attrList) do
            if attrinfo.level <= lastlevel then
                common.logger.Error(" @张名璐，绝世武功，group.csv中id={0}记录中 组激活属性－重数　未按升序填写", id)
                break ;
            end
            lastlevel = attrinfo.level
        end
        
        for _, sid in ipairs(groupcfg.stuntList) do
            local stuntcfg = cfg.skill.stunt.stunt.get(sid, 1)
            if not stuntcfg then
                common.logger.Error(" @张名璐，绝世武功，group.csv中id={0}的记录中绝技={1} 在stunt.csv里不存在", groupcfg.id, sid)
            elseif stuntcfg.group ~= groupcfg.id then
                common.logger.Error(" @张名璐，绝世武功，group.csv中id={0}的记录 和 stunt.csv里id={1}, 两者的group值不匹配", groupcfg.id, sid)
            end
        end
    end
    
    local continue = true
    
    --- stunt.csv
    local stuntall = cfg.skill.stunt.stunt.all
    local visit = {}
    for id, stuntcfg in pairs(stuntall) do
        if not visit[stuntcfg.id] then
            visit[stuntcfg.id] = true
            
            local hassid = false
            for id, item in pairs(bookall) do
                if item.tid == stuntcfg.id then
                    hassid = true
                end
            end
            if not hassid then
                common.logger.Error(" @唐鑫，绝世武功，stunt.csv中id={0} 在book.csv中无法学到", stuntcfg.id)
                continue = false
            end
            
            local skillcfg = cfg.skill.skill.get(stuntcfg.id)
            if not skillcfg then
                common.logger.Error(" @唐鑫，绝世武功，stunt.csv中id={0} 在skill.csv中不存在", stuntcfg.id)
                continue = false
            end
            for i = 1, skillcfg.maxLevel do
                if not cfg.skill.stunt.stunt.get(stuntcfg.id, i) then
                    common.logger.Error(" @唐鑫，绝世武功，stunt.csv中id={0} 在skill.csv配置的最大重数是{1}，但stunt.csv中缺少id={2},level={3}的记录行数据", stuntcfg.id, skillcfg.maxLevel, stuntcfg.id, i)
                    continue = false
                end
            end
        end
    end
    
    if not continue then
        return
    end
    
    stuntall = cfg.skill.stunt.stunt.all
    visit = {}
    for id, stuntcfg in pairs(stuntall) do
        if not visit[stuntcfg.id] then
            visit[stuntcfg.id] = true
            local skillcfg = cfg.skill.skill.get(stuntcfg.id)
            for i = 1, skillcfg.maxLevel do
                local stepList = cfg.skill.stunt.stunt.get(stuntcfg.id, i).stepList
                if #stepList > 0 then
                    local baseattrlist = stepList[1].RefValue.attrList
                    if baseattrlist == nil then
                        common.logger.Error(" @出错csv中stuncfg.id={0}, level={1} ", stuntcfg.id, i)
                        break
                    end
                    for j = 1, #(stepList) do
                        local curattrlist = stepList[j].RefValue.attrList
                        local flag = false
                        if (#curattrlist) ~= (#baseattrlist) then
                            flag = true
                        else
                            for m = 1, (#baseattrlist) do
                                if baseattrlist[m].RefId ~= curattrlist[m].RefId then
                                    flag = true
                                    break
                                end
                            end
                        end
                        if flag then
                            common.logger.Error(" @张名璐，绝世武功，stunt.csv中id={0} level={1}, 阶段{2} 和 阶段{3} 加的属性类型不一致", stuntcfg.id, i, 1, j)
                        end
                        
                        local itemlist = stepList[1].RefCid.itemList
                        for p, costinfo in ipairs(itemlist) do
                            if costinfo.RefType == cfg.skill.stunt.costtype.Coin then
                                if not cfg.common.coin.get(costinfo.id) then
                                    common.logger.Error(" @张名璐，绝世武功，stunt.csv中id={0} level={1}, 阶段={2} 突破消耗的金钱填写错误，coin.csv中没有！", stuntcfg.id, i, j)
                                end
                            end
                            if costinfo.RefType == cfg.skill.stunt.costtype.CommonItem then
                                local bookcfg = cfg.skill.stunt.book.get(costinfo.id)
                                if not bookcfg then
                                    common.logger.Error(" @张名璐，绝世武功，stunt.csv中id={0} level={1}, 阶段={2} 突破消耗的道具填写错误，book.csv中没有！", stuntcfg.id, i, j)
                                end
                                if bookcfg and bookcfg.type == "Book" and bookcfg.tid ~= stuntcfg.id then
                                    common.logger.Error(" @张名璐，绝世武功，stunt.csv中id={0} level={1}, 阶段={2} 突破消耗的技能书居然不是{3}！，填错了吗？", stuntcfg.id, i, j, bookcfg.name)
                                end
                            end
                        end
                    end
                    
                    local flag = {}
                    for m = 1, (#baseattrlist) do
                        if flag[baseattrlist[m].RefId] then
                            common.logger.Error(" @张名璐，绝世武功，stunt.csv中id={0} level={1}, 阶段{2} 加的属性有重复的", stuntcfg.id, i, 1)
                            break
                        end
                        flag[baseattrlist[m].RefId] = true
                    end
                end
                
                
                --RefType
            end
        end
    end
end

return checkStunt

