local common = require "common.common"
local cfg = require "cfg._cfgs"
local checkStunt = require "test.checkStunt"
local taskhelper = require "module.story.task.Helper"
local Beans = require("cfg._beans")

local cfgChecker = {}

--用于检查在打表阶段不能检查的逻辑错误
function cfgChecker.startCheck()
    if common.debugutils.isEditor then
        cfgChecker.checkWeaponModel()
        cfgChecker.checkJumpPoint()
        --cfgChecker.checkSkillCfg()
        --cfgChecker.checkIdValid()
        --cfgChecker.checkStuntCfg()
        cfgChecker.checkUseitemTask()
        cfgChecker.checkTaskChoice()
    end
end

function cfgChecker.checkWeaponModel()
    for id, equipattr in pairs(cfg.equip.equipattr.all) do
        if equipattr.component == 1 then
            if equipattr.NullableRefMalePart == nil or equipattr.NullableRefFemalePart == nil then
                local equipName = cfg.item.commonitem.get(id).name
                common.logger.WarnInEditor("ID:" .. id .. ", " .. equipName .. "，配置为武器，但是没有填写对应的partID")
            end

            --if cfg.equip.equiprefineeffect.get(id) == nil then
            --    local equipName = cfg.item.commonitem.get(id).name
            --    common.logger.Warn("ID:" .. id .. ", " .. equipName .. "，配置为武器，但是在精炼特效表没有找到这个ID")
            --end
        end
    end
end

function cfgChecker.checkJumpPoint()
    for id, cfgjump in pairs(cfg.map.jump.all) do
        if #cfgjump.angleYList <= 0 then
            common.logger.Error("ID:" .. id .. ", " .. "跳跃点配置：角度list长度为零@策划")
        end
        if #cfgjump.angleYList > 0 then
            if #cfgjump.angleYList - #cfgjump.timeList ~= 1 then
                common.logger.Error("ID:" .. id .. ", " .. "跳跃点配置：角度和跳跃间隔时间不一致@策划")
            end
        end
        for dodgeid, time in pairs(cfgjump.timeList) do
            local cfgdodgecurve = cfg.asset.dodgecurve.get(dodgeid)
            if time > cfgdodgecurve.coldTime then
                common.logger.Error("ID:" .. id .. ", " .. "跳跃点配置：跳跃间隔时间 xiaoyu cd时间@策划")
            end
        end
    end
end

function cfgChecker.checkSkillCfg()
    -- 职业技能表数据合法性检测
    local professionskill = cfg.profession.professionskill.all
    for i, info in pairs(professionskill) do
        if not cfg.profession.profession.get(info.id) then
            common.logger.Error("--- @ 配技能的策划大佬，professionskill.csv中, 职业id: " .. info.id .. " 在profession.csv中不存在！ ")
        end
        if #(info.attackList) < 1 then
            common.logger.Error("--- @ 配技能的策划大佬，professionskill.csv中, 职业id: " .. info.id .. " 的记录没有配置普攻！ ")
        end
        if not cfg.skill.skill.get(info.dodgeId) then
            common.logger.Error("--- @ 配技能的策划大佬，professionskill.csv中, 职业id: " .. info.id .. " 的dodgeId :" .. info.dodgeId .. " 在skill.csv中不存在！")
        end
        cfgChecker.checkSkillTag(cfg.skill.skill.get(info.dodgeId), Beans.skill.skilllogic.dodge:type())
        for m = 1, #(info.attackList) do
            if not cfg.skill.skill.get(info.attackList[m]) then
                common.logger.Error("--- @ 配技能的策划大佬，professionskill.csv中, 职业id: " .. info.id .. " 的Attack" .. m .. ":" .. info.attackList[m] .. " 在skill.csv中不存在！")
            end
        end
        for m = 1, #(info.skillIDList) do
            if not cfg.skill.skill.get(info.skillIDList[m]) then
                common.logger.Error("--- @ 配技能的策划大佬，professionskill.csv中, 职业id: " .. info.id .. " 的SkillID" .. m .. ":" .. info.skillIDList[m] .. " 在skill.csv中不存在！")
            end
        end
    end

    -- skill.csv和skilllevel.csv一致性检测
    local skillmap = {}
    for skillid, skillcfg in pairs(cfg.skill.skill.all) do
        for i = 1, skillcfg.maxLevel do
            local skilllevelcfg = cfg.skill.skilllevel.get(skillid, i)
            if not skilllevelcfg then
                common.logger.Error("--- @ 配技能的策划大佬，skill.csv中, 技能id: " .. skillid .. " 的最大等级:" .. skillcfg.maxLevel .. ", 但 SkillId:" .. skillid .. " SkillLevel:" .. i .. " 在skilllevel.csv中不存在！")
            else
                if skillcfg.RefSkillClass == cfg.skill.skillclass.Chant or skillcfg.RefSkillClass == cfg.skill.skillclass.Channel then
                    if skilllevelcfg.progressBarDuration <= 0 then
                        common.logger.Error("--- @ 配技能的策划大佬，skill.csv中, 技能id: " .. skillid .. " 是读条技，但skilllevel.csv中skillid:" .. skillid .. ",level:" .. i .. " 读条时长是非法值")
                    end
                end
                local buffcfgs = { skilllevelcfg.NullableRefAddBuffId, skilllevelcfg.NullableRefRideAddBuffId }
                for i, buffcfg in pairs(buffcfgs) do
                    if buffcfg.logic:type() ~= Beans.buff.bufflogic.effectbuff:type() then
                        common.logger.Error("--- @ 配技能的策划大佬，skilllevel.csv中, skillid:" .. skillid .. " 配置的buff:" .. (buffcfg.id) .. " 不是EffectBuff类型")
                    end
                end
            end
        end
    end

    local CommonSkillIdList = {
        [1] = { skillcfg = cfg.skill.skill.get(1), typetag = Beans.skill.skilllogic.teleport:type() },
        [2] = { skillcfg = cfg.skill.skill.get(2), typetag = Beans.skill.skilllogic.opentreasurebox:type() },
        [3] = { skillcfg = cfg.skill.skill.get(3), typetag = Beans.skill.skilllogic.hunt:type() },
        [4] = { skillcfg = cfg.skill.skill.get(4), typetag = Beans.skill.skilllogic.carry:type() },
        [5] = { skillcfg = cfg.skill.skill.get(5), typetag = Beans.skill.skilllogic.decarry:type() },
    }
    for i, item in pairs(CommonSkillIdList) do
        cfgChecker.checkSkillTag(item.skillcfg, item.typetag)
    end
end

function cfgChecker.checkSkillTag(skillcfg, typetag)
    local skilllevelcfg = cfg.skill.skilllevel.get(skillcfg.iD, 1)
    local bTagValid = skilllevelcfg and (skilllevelcfg.skillLogic:type() == typetag)
    if not bTagValid then
        common.logger.Error("--- @ 配技能的策划大佬，skilllevel.csv中, skillid:" .. (skillcfg.iD) .. " level:1 " .. " 的skillLogic 不是" .. typetag)
    end
end


function cfgChecker.checkStuntCfg()
    checkStunt.Check()
end

function cfgChecker.checkIdValid()
    local skills = cfg.skill.skill.all
    for i, skillCfg in pairs(skills) do
        if skillCfg.iD < 1 then
            common.logger.Error("--- @ 配技能的策划大佬，skill.csv中, id:" .. (skillCfg.iD) .. " 非法，需为大于0的整数 ")
        end
    end

    local buffs = cfg.buff.buff.all
    for i, buffCfg in pairs(buffs) do
        if buffCfg.id < 1 then
            common.logger.Error("--- @ 配技能的策划大佬，buff.csv中, id:" .. (buffCfg.id) .. " 非法，需为大于0的整数 ")
        end
    end

    local all = cfg.skill.skillext.skillcostbook.all
    for i, info in pairs(all) do
        local skilllevelcfg = cfg.skill.skilllevel.get(info.id, info.level)
        if skilllevelcfg == nil then
            common.logger.Error("--- @ 配技能的策划大佬， skillcostbook.csv中, 技能id:" .. (info.id) .. ",level:" .. (info.level) .. " 在skilllevel.csv中没有 ")
        end
    end
end

function cfgChecker.checkUseitemTask()
   
end

function cfgChecker.checkTaskChoice()
    local cfgnpcs = cfg.npc.npc.all
    for _, cfgnpc in pairs(cfgnpcs) do
        if cfgnpc.NullableRefChoiceid then
            if cfgnpc.NullableRefChoiceid.funcA == 3 or cfgnpc.NullableRefChoiceid.funcB == 3 then
                common.logger.Error("panelchoice 按钮配错， 没有此功能的按钮 , taskchoice表id-----{0}------", cfgnpc.NullableRefChoiceid.id)
            end
        end
    end
end


function cfgChecker.checkChangeFashion()
    local cfgfashions = cfg.fashioncloth.fashioncloth.all
    local cfgchangesexfashions=cfg.changesex.changesexfashions.all
    for _, cfgfashion in pairs(cfgfashions) do
        if cfgfashion.needchangesex then
            local isok=false
            for _,cfgchangesexfashion in pairs(cfgchangesexfashions) do
                if cfgchangesexfashion.malefashion ==cfgfashion.id then
                    isok=true
                elseif cfgchangesexfashion.femalefashion ==cfgfashion.id then
                    isok=true
                end
            end
            if not isok then
                common.logger.Error("cfgfashion  needchangesex true not find fashionid -----{0}------int changesexfashion", cfgfashion.id)
            end
        end
    end
end

return cfgChecker